/* eslint-disable camelcase */
import app from 'express';
import moment from 'moment';
import fs from 'fs';
import path from 'path';
import { celebrate } from 'celebrate';
import Correo from '../utilidades/correo';
import { logging } from '../utilidades/logs';
import { Desencriptar } from '../utilidades/encriptacion';
import { crearConexionSequelize } from '../configuraciones/bd';
import { consultaConfiguracionBD } from '../configuraciones/conexionSql';
import { peticionEnvioCorreosSchema, regexBase64 } from '../configuraciones/constantes';

const router = app.Router();

// Definir el directorio base
const BASE_DIRECTORY = process.cwd();

function safePathTraversal(userInput) {
  // Combina el directorio base con la entrada del usuario
  const requestedPath = path.join(BASE_DIRECTORY, userInput);
  console.log(userInput);
  logging(true, "requestedPath => ", requestedPath);
  // Asegúrate de que la ruta solicitada está dentro del directorio permitido
  if (!requestedPath.startsWith(BASE_DIRECTORY)) {
    throw new Error('Path traversal detected');
  }
  return requestedPath;
}

/**
 * Petición para envio de correos
 */
router.post('/', celebrate({ body: peticionEnvioCorreosSchema }), async (req, res, next) => {
  try {
    // Variables
    const { usuario: { idu_usuario_correo }, body } = req;
    const correos = !Array.isArray(body) ? [body] : body;

    // Datos de configuracion de conxión a personal
    const datosConexionPersonal = {
      server: process.env.HOSTPERSONAL,
      user: Desencriptar(process.env.USERPERSONAL),
      password: Desencriptar(process.env.CLAVEPERSONAL),
      database: Desencriptar(process.env.BDPERSONAL),
      port: parseInt(process.env.PUERTOPERSONAL),
    }

    // Se consultan los datos para la conexion a la bd de postgres
    const datosConexionPostgresBD = await consultaConfiguracionBD(datosConexionPersonal, process.env.IDACCESOSPOSTGRES, 10);

    // Se crea objeto de conexion a postgres
    const datosConexionPostgres = {
      host: datosConexionPostgresBD.IP.trim(),
      username: datosConexionPostgresBD.USUARIO.trim(),
      password: datosConexionPostgresBD.PASSWORD.trim(),
      database: process.env.BDFACTURACIONELECTRONICA,
      port: process.env.PUERTOPOSTGRES,
    }

    // Se crea conexion a la bd de postgres y se obtiene el modelo para consultar la configuración del correo
    const { cat_correo_configuracion } = await crearConexionSequelize(datosConexionPostgres);

    const configCorreo = await cat_correo_configuracion.findOne({
      attributes: [
        'des_host',
        'des_proxy',
        'des_usuario',
        'des_clave',
        'des_correo_salida',
        'num_puerto',
      ],
      where: {
        idu_usuario_correo,
        opc_estatus: true,
      }
    });

    // // Se abre conexión con el servidor de correos
    const conexionCorreo = new Correo(configCorreo);
    const promises = [];
    let archivosAdjuntos = [];
    // Envio de correos
    for (const correo of correos) {
      const { asunto, archivos, destinatario, html } = correo;
      archivosAdjuntos = [];
      if (archivos) {
        for (const archivo of archivos) {
          const { archivo: archivoURL, nombreArchivo } = archivo;

          // Se valida si el archivo es un base64
          const base64 = regexBase64.test(archivoURL);

          // Se crea el objeto del archivo adjunto dependiendo si es un base64 o una url
          const documento = base64 ?
            { filename: nombreArchivo, content: archivoURL.toString('base64'), encoding: 'base64' } :
            { filename: nombreArchivo, path: archivoURL };

          archivosAdjuntos.push(documento);
        }
      }

      // Se manda el correo
      promises.push(
        conexionCorreo.enviarCorreo(asunto, archivosAdjuntos, destinatario, html)
      );
    }

    // // Se espera a que el servidor de correos responda
    const respCorreo = await Promise.all(promises);

    // Respuesta
    return res.status(200).send(respCorreo);
  } catch (error) {
    logging(true, "Error envio de correo: ", error);
    return res.status(501).send(error);
  }
});

/**
 * Petición para descargar los logs de una fecha específica
 */
router.get('/getLogs', async (req, res, next) => {
  const directorioLog = 'logs';
  const directorioError = 'errores';
  const { query: { fecha } } = req;
  if (!fecha) {
    return res.status(400).send('Fecha es requerida.');
  }
  try {
    const rutaLog = `${directorioLog}/${moment(fecha).format('MMM-yyyy')}/${directorioError}/${fecha}.log`;
    const rutaSegura = safePathTraversal(rutaLog);
    if (fs.existsSync(rutaSegura)) {
      res.download(rutaSegura, (err) => {
        if (err) {
          res.status(501).send('Error al descargar el archivo.');
        }
      });
    } else {
      res.status(404).send('Archivo no encontrado.');
    }
  } catch (err) {
    res.status(403).send('Permiso Denegado.');
  }
});

export default router;