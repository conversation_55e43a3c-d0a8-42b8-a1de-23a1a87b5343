/* eslint-disable camelcase */
import app from 'express';
import jwt from 'jsonwebtoken';
import { celebrate } from 'celebrate';
import { logging } from '../utilidades/logs';
import { Desencriptar } from '../utilidades/encriptacion';
import { crearConexionSequelize } from '../configuraciones/bd';
import { consultaConfiguracionBD } from '../configuraciones/conexionSql';
import { CONTRASENA_INCORRECTA, USUARIO_NO_ENCONTRADO } from '../configuraciones/mensajes';
import { tokenSchema } from '../configuraciones/constantes';
import configGeneral from '../package.json';

const router = app.Router();

/**
 * Petición validar usuario y crear token
 */
router.post('/', celebrate({ body: tokenSchema }), async (req, res) => {
    try {
        // Variables
        const { nom_usuario, des_clave } = req.body;

        // Datos de configuracion de conxión a personal
        const datosConexionPersonal = {
            server: process.env.HOSTPERSONAL,
            user: Desencriptar(process.env.USERPERSONAL),
            password: Desencriptar(process.env.CLAVEPERSONAL),
            database: Desencriptar(process.env.BDPERSONAL),
            port: parseInt(process.env.PUERTOPERSONAL),
        }

        // Se consultan los datos para la conexion a la bd de postgres
        const datosConexionPostgresBD = await consultaConfiguracionBD(datosConexionPersonal, process.env.IDACCESOSPOSTGRES, 10);

        // Se crea objeto de conexion a postgres
        const datosConexionPostgres = {
            host: datosConexionPostgresBD.IP.trim(),
            username: datosConexionPostgresBD.USUARIO.trim(),
            password: datosConexionPostgresBD.PASSWORD.trim(),
            database: process.env.BDFACTURACIONELECTRONICA,
            port: process.env.PUERTOPOSTGRES,
        }

        // Se crea conexión a la bd de postgres y se obtiene el modelo para consultar la información del usario
        const { cat_usuario_correo } = await crearConexionSequelize(datosConexionPostgres);

        const usuario = await cat_usuario_correo.findOne({
            attributes: ['idu_usuario_correo', 'nom_usuario', 'des_clave'],
            where: {
                nom_usuario,
                opc_estatus: true,
            }
        });

        // Se valida si se encontro el usuario
        if (!usuario) {
            return res.status(404).send(USUARIO_NO_ENCONTRADO);
        }

        // Se valida que las contraseñas coincidan
        if (usuario.des_clave !== des_clave) {
            return res.status(404).send(CONTRASENA_INCORRECTA);
        }

        // Se crea el token con solo la información necesaria (excluyendo datos sensibles como la contraseña)
        const tokenPayload = {
            idu_usuario_correo: usuario.idu_usuario_correo,
            nom_usuario: usuario.nom_usuario,
            opc_estatus: usuario.opc_estatus,
            fec_registro: usuario.fec_registro
        };
        const token = jwt.sign(tokenPayload, process.env.CLAVESECRETA, { expiresIn: process.env.TIEMPOEXPIRACION });

        // Respuesta
        return res.status(200).send(token);
    } catch (error) {
        logging(true, "Error Generacion de token: ", error);
        return res.status(501).send();
    }
});

/**
 * Peticion para retornar la version de API
 */
router.get('/getVersion', (req, res) => {
    const { version } = configGeneral;
    return res.status(200).send('1.0.1');
});

export default router;