import cors from 'cors';
import { errors } from 'celebrate';
import BodyParser from 'body-parser';
import Compression from 'compression';
import rateLimit from 'express-rate-limit';
import headers from './middlewares/headers';
import logErrores from './middlewares/logErrores';
import validarToken from './middlewares/validarToken';
import token from './controladores/token';
import correo from './controladores/correo';
import { limiterOptions } from './configuraciones/constantes';

export default (app) => {
  app.use(Compression());
  app.use(cors({ origin: true, credentials: true }));
  app.use(BodyParser.urlencoded({ limit: '5mb', extended: true }));
  app.use(BodyParser.json({ limit: '5mb', extended: true }));

  // MIDDLEWARE para Cabeceros que serán validos para todas las peticiones
  app.use(headers);
  app.use(rateLimit(limiterOptions));
  // Configuración de HSTS
  app.use((req, res, next) => {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    next();
  });
  // rutas endpoint
  app.use('/api/v1/token', token);
  app.use('/api/v1/correo', validarToken, correo);

  app.use(errors());

  app.use((err, req, res, next) => {
    const statusCode = err.statusCode || 501;
    const mensaje = 'Error en el servidor';
    res.status(statusCode).json({ error: mensaje });
  });

  // MIDDLEWARE para cachar errores y generar el log
  app.use(logErrores);
};
