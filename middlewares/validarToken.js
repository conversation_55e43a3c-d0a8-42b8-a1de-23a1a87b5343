import jwt from 'jsonwebtoken';

const validarToken = (req, res, next) => {
  if (req.method === 'OPTIONS') { return next(); }

  const token = req.get("APITOKEN")

  if (!token) {
    return res.status(403).send("Ocurrió un error al validar el token.");
  }

  try {
    req.usuario = jwt.verify(token, process.env.CLAVESECRETA);
  } catch (err) {
    return res.status(403).send("La sesión ha caducado");
  }
  return next();
};

module.exports = validarToken;
