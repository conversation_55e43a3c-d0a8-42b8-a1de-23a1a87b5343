const path = require('path');
const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  //stats: 'errors-only',
  stats: 'normal',
  entry: {
    "api-envio-correos": "./index.js",
  },
  target: 'node',
  devtool: 'source-map',
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development',
  output: {
    path: path.resolve(__dirname, 'build'),
    filename: '[name].js',
    publicPath: 'build/',
  },
  externals: [nodeExternals()],
  optimization: {
    minimizer: [new TerserPlugin()],
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.env': {
        NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'development'),
        PORT: JSON.stringify(process.env.PORT),
        HOSTPERSONAL: JSON.stringify(process.env.HOSTPERSONAL),
        USERPERSONAL: JSON.stringify(process.env.USERPERSONAL),
        CLAVEPERSONAL: JSON.stringify(process.env.CLAVEPERSONAL),
        BDPERSONAL: JSON.stringify(process.env.BDPERSONAL),
        PUERTOPERSONAL: JSON.stringify(process.env.PUERTOPERSONAL),
        IDACCESOSPOSTGRES: JSON.stringify(process.env.IDACCESOSPOSTGRES),
        PUERTOPOSTGRES: JSON.stringify(process.env.PUERTOPOSTGRES),
        CLAVESECRETA: JSON.stringify(process.env.CLAVESECRETA),
        TIEMPOEXPIRACION: JSON.stringify(process.env.TIEMPOEXPIRACION),
        BDFACTURACIONELECTRONICA: JSON.stringify(process.env.BDFACTURACIONELECTRONICA),
      },
    }),
  ],
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            cacheDirectory: true
          }
        }
      },
    ],
  },
  resolve: {
    extensions: ['.js']
  }
};
