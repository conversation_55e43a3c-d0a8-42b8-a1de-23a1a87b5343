# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore
logs/
# Node artifact files
node_modules/
cargas/
dist/
build/
package-lock.json

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/
dist/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files
*.mp4
*.tiff
*.avi
*.flv
*.mov
*.wmv

# Ignorar carpeta reportes
public/reportes/

# Ignorar archivos de configuración de visual code
.vscode/

# Ignorar cargas de imagenes a excepción de la por defecto
cargas/*
!cargas/no-image.jpeg