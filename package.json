{"name": "api-envio-correo", "version": "1.0.1", "description": "aplicación para el envio de correos", "main": "index.js", "scripts": {"build": "webpack", "start": "node ./build/api-envio-correos.js", "dev": "PORT=4001 NODE_ENV=\"development\" npm-run-all --parallel watch:server watch:build", "windows": "SET PORT=4001 && SET NODE_ENV=development&& npm-run-all --parallel watch:server watch:build", "modelspsg": "sequelize-auto -h \"localhost\" -d envio-correo -u postgres -x \"123\" -p 5432 -e postgres -o \"./modelospsg\"", "watch:build": "webpack --watch", "watch:server": "nodemon --inspect=\"9229\" \"./build/api-envio-correos.js\" --watch \"./build\" ", "watch:lint": "node node_modules/eslint-watch/bin/esw -w --fix", "lint": "eslint ./ --fix"}, "author": "Gaman Solutions", "license": "ISC", "dependencies": {"body-parser": "^2.2.0", "celebrate": "^15.0.3", "compression": "^1.8.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mssql": "^11.0.1", "nodemailer": "^7.0.3", "pg": "^8.16.0", "regenerator-runtime": "^0.14.1", "sequelize": "^6.37.7", "sequelize-auto": "^0.8.8", "tedious": "18.6.1", "winston": "^3.17.0", "@babel/runtime": "^7.22.5"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-runtime": "^7.22.5", "@babel/preset-env": "^7.22.5", "babel-loader": "^9.1.2", "chai": "^4.3.1", "chai-http": "^4.3.0", "eslint": "^6.8.0", "eslint-config-airbnb": "^18.1.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-react": "^7.19.0", "eslint-watch": "^6.0.1", "husky": "^5.1.3", "nodemon": "^1.12.1", "npm-run-all": "^4.1.1", "terser-webpack-plugin": "^5.3.9", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0", "acorn": "^8.10.0"}, "overrides": {"serialize-javascript": "6.0.2", "ansi-regex": "5.0.1", "braces": "3.0.3", "cross-spawn": "7.0.6", "glob-parent": "6.0.2", "glob": "11.0.2", "json5": "2.2.3", "micromatch": "4.0.8", "debug": "4.4.1", "bluebird": "3.7.2", "cacache": "12.0.4", "got": "14.4.7"}}