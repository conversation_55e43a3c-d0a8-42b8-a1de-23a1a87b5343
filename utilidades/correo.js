import nodemailer from 'nodemailer';
import { logging } from './logs';
import { puertosTLS } from '../configuraciones/constantes';
import { ERRORTIMEOUTPROXY } from '../configuraciones/mensajes';

class EmailSender {
  /**
   * Contrcutor para la clase correo
   * @param {*} configuraciones Datos para configurar el servidor de correo
   */
  constructor(configuraciones) {

    const { des_host, des_proxy, des_usuario, des_clave, des_correo_salida, num_puerto } = configuraciones;
    const seguridad = puertosTLS.includes(parseInt(num_puerto, 10));
    const esDesarrollo = process.env.NODE_ENV === 'development';

    try {
      this.transporter = nodemailer.createTransport({
        host: des_host,
        port: num_puerto,
        secure: seguridad,
        auth: {
          user: des_usuario,
          pass: des_clave
        },
        debug: esDesarrollo,
        logger: esDesarrollo,
        pool: true,
        maxConnections: 1,
        proxy: des_proxy,
      });
      this.mailOptions = {
        from: des_correo_salida,
      };
    } catch (error) {
      logging(true, `Error Proceso envio correo: `, error);
    }
  }

  /**
   * 
   * @param {*} asunto Titulo del correo
   * @param {*} archivo Documento adjunto al correo
   * @param {*} destinatario Correo al que se enviara el talon
   * @param {*} html Contenido del mensaje
   * @returns 
   */
  enviarCorreo(asunto, archivos = null, destinatario, html) {
    try {
      const opcionesCorreo = {
        from: this.mailOptions.from,
        to: destinatario,
        subject: asunto,
        html,
        attachments: archivos
      };

      return new Promise((resolve, reject) => {
        this.transporter.sendMail(opcionesCorreo, (error, info) => {
          if (error) {
            logging(true, `Error proceso envio correo: `, error);
            if (error.message == ERRORTIMEOUTPROXY) {
              reject(error);
            } else {
              resolve({ ...informacion, error: true, info });
            }
          } else {
            resolve(info);
          }
        });
      });
    } catch (error) {
      logging(true, `Error proceso envio correo: `, error);
    }
  }
}

export default EmailSender;
